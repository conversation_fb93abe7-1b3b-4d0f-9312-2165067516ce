<template>
  <div class="rack-detail-container">
    <div class="page-header">
      <div class="back-button">
        <el-button link @click="goBack">
          <el-icon><ArrowLeft /></el-icon>
          返回机柜管理
        </el-button>
      </div>
      <div class="rack-title">
        <h1>{{ rack.name }} <span class="rack-location-label">标准{{ rack.total_u }}U机柜</span></h1>
      </div>
      <div class="rack-status">
        <el-tag :type="rack.status === 'normal' ? 'success' : 'danger'" effect="light" size="large" round>
          <el-icon v-if="rack.status === 'normal'"><CircleCheck /></el-icon>
          <el-icon v-else><Warning /></el-icon>
          {{ rack.status === 'normal' ? '正常' : '告警' }}
        </el-tag>
        <el-button type="primary" @click="editRackInfo">
          <el-icon><Edit /></el-icon> 编辑
        </el-button>
      </div>
    </div>
    
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stat-cards">
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="card-content">
            <div class="card-value-section">
              <div class="card-value">{{ rack.used_u }}/{{ rack.total_u }}</div>
              <div class="card-label">机柜容量</div>
              <div class="card-progress">
                <el-progress 
                  :percentage="usagePercentage" 
                  :color="getProgressColor(usagePercentage)"
                  :show-text="false"
                />
              </div>
            </div>
            <div class="card-icon">
              <el-icon><Grid /></el-icon>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="card-content">
            <div class="card-value-section">
              <div class="card-value">{{ devices.length }}</div>
              <div class="card-label">设备数量</div>
              <div class="card-extra-info">
                在线: {{ onlineDevicesCount }} 台
              </div>
            </div>
            <div class="card-icon">
              <el-icon><Monitor /></el-icon>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="card-content">
            <div class="card-value-section">
              <div class="card-value">{{ rack.temperature }}°C</div>
              <div class="card-label">温度</div>
              <div class="card-extra-info">
                {{ getTemperatureStatus(rack.temperature) }}
              </div>
            </div>
            <div class="card-icon">
              <el-icon><Reading /></el-icon>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="card-content">
            <div class="card-value-section">
              <div class="card-value">{{ rack.power }}kW</div>
              <div class="card-label">功率</div>
              <div class="card-progress">
                <el-progress 
                  :percentage="powerPercentage" 
                  :color="getProgressColor(powerPercentage)"
                  :show-text="false"
                />
              </div>
            </div>
            <div class="card-icon">
              <el-icon><Lightning /></el-icon>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 标签页内容 -->
    <el-card class="content-card">
      <el-tabs v-model="activeTab" class="demo-tabs">
        <el-tab-pane label="设备列表" name="devices">
          <div class="device-view-container">
            <div class="device-list-panel">
              <div class="panel-header">
                <div class="device-count">
                  共 {{ devices.length }} 个设备
                  <span v-if="devices.length === 0" class="empty-hint">(机柜中暂无设备，请先导入设备或点击刷新按钮)</span>
                </div>
              </div>
              <div class="device-table">
                <rack-device-list 
                  :devices="devices" 
                  @select-device="handleDeviceAction"
                  @remove-device="confirmRemoveDevice"
                  @edit-device="handleEditDevice"
                  :empty-text="'此机柜中暂无设备'"
                />
              </div>
              <div class="add-device-btn-container">
                <el-button type="primary" @click="showAddDeviceDialog" class="add-device-btn">
                  <el-icon><Plus /></el-icon>
                  添加设备
                </el-button>
                <!-- <el-button type="success" @click="showImportDevicesDialog" class="import-btn">
                  <el-icon><Upload /></el-icon>
                  导入设备
                </el-button>
                <el-button type="info" @click="fetchDevices(rack.id)" class="refresh-btn">
                  <el-icon><Refresh /></el-icon>
                  刷新设备列表
                </el-button> -->
              </div>
            </div>
            
            <div class="rack-visualization-panel">
              <div class="rack-visualization-wrapper">
                <rack-layout-canvas 
                  :racks="[rack]" 
                  :selectedRackId="rack.id"
                  @select-device="handleDeviceAction"
                  @remove-device="confirmRemoveDevice"
                  @request-add-rack="showAddDeviceDialog"
                  @edit-device="handleEditDevice"
                  @add-device-at-position="handleAddDeviceAtPosition"
                />
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="机柜视图" name="rackView">
          <div class="full-rack-view">
            <rack-layout-canvas 
              :racks="[rack]" 
              :selectedRackId="rack.id"
              @select-device="handleDeviceAction"
              @remove-device="removeDevice"
              @request-add-rack="showAddDeviceDialog"
              @edit-device="handleEditDevice"
              @add-device-at-position="handleAddDeviceAtPosition"
            />
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>
    
    <!-- 对话框组件 -->
    <add-device-dialog
      v-model:visible="addDeviceDialogVisible"
      :rack-id="rack.id"
      :available-positions="availablePositions"
      :initial-position="initialPosition"
      @add-device="handleAddDevice"
    />
    
    <edit-device-dialog
      v-model:visible="editDeviceDialogVisible"
      :device="selectedDevice"
      :rack-total-u="rack.total_u"
      @update-device="handleUpdateDevice"
    />
    
    <edit-rack-dialog
      v-model:visible="editRackDialogVisible"
      :rack="rack"
      @update:rack="handleUpdateRack"
    />
    
    <!-- 导入设备对话框 -->
    <import-export-rack-dialog
      v-model:visible="importDevicesDialogVisible"
      :availableRacks="[rack]"
      :currentRackId="rack.id"
      :importMode="'devices-only'"
      @import-completed="handleImportCompleted"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { 
  ArrowLeft, Plus, CircleCheck, CircleClose, Edit, 
  Grid, Monitor, Reading, Lightning, Warning, Refresh, Upload
} from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus';
import axios from 'axios';
import AddDeviceDialog from '@/components/rackManagement/dialogs/AddDeviceDialog.vue';
import EditRackDialog from '@/components/rackManagement/dialogs/EditRackDialog.vue';
import RackLayoutCanvas from '@/components/rackManagement/RackLayoutCanvas.vue';
import RackDeviceList from '@/components/rackManagement/RackDeviceList.vue';
import EditDeviceDialog from '@/components/rackManagement/dialogs/EditDeviceDialog.vue';
import ImportExportRackDialog from '@/components/rackManagement/dialogs/ImportExportRackDialog.vue';

const API_URL = '/api/rack-management';

const router = useRouter();
const route = useRoute();
const activeTab = ref('devices');
const editRackDialogVisible = ref(false);
const addDeviceDialogVisible = ref(false);
const editDeviceDialogVisible = ref(false);
const importDevicesDialogVisible = ref(false);
const selectedDeviceId = ref(null);
const selectedDevice = ref(null);
const initialPosition = ref(null);
const loading = ref(false);

// 初始化数据
const rack = ref({
  id: '',
  name: '',
  location: '',
  total_u: 42,
  used_u: 0,
  temperature: 0,
  power: 0,
  status: 'normal'
});

const devices = ref([]);

// 计算属性
const usagePercentage = computed(() => {
  if (!rack.value.total_u) return 0;
  return Math.round((rack.value.used_u / rack.value.total_u) * 100);
});

const powerPercentage = computed(() => {
  if (!rack.value.max_power) return 0;
  return Math.round((rack.value.power / rack.value.max_power) * 100);
});

const onlineDevicesCount = computed(() => {
  return devices.value.filter(device => device.status === 'online').length;
});

// 获取可用的位置
const availablePositions = computed(() => {
  const occupied = new Set();
  
  // 记录调试信息
  console.log(`计算机柜可用位置，当前设备列表:`, devices.value);
  
  // 标记被占用的位置
  // 对于每个设备，根据其位置和高度，将所有占用的U位添加到occupied集合中
  devices.value.forEach(device => {
    // 确保设备数据有效
    if (!device.position || !device.u_size) {
      console.warn(`设备数据不完整: ${device.name} (${device.id}), 位置=${device.position}, 高度=${device.u_size}`);
      return;
    }
    
    console.log(`设备 ${device.name} (${device.id}) 占用位置: ${device.position}U, 高度: ${device.u_size}U`);
    
    // 计算设备占用的所有U位
    // 设备从底部向上安装，但U位编号是从下往上增加的
    // 例如：一个位于5U、高度为3U的设备会占用5、6、7三个U位
    const occupiedPositions = [];
    for (let i = 0; i < device.u_size; i++) {
      const pos = device.position + i; // 修正：从底部位置向上计算
      if (pos > 0 && pos <= rack.value.total_u) { // 确保位置有效且不超出机柜范围
        occupied.add(pos);
        occupiedPositions.push(pos);
      }
    }
    console.log(`  - 占用的U位: ${occupiedPositions.join(', ')}U`);
  });
  
  // 计算可用位置（未被占用的位置）
  const available = [];
  for (let u = 1; u <= rack.value.total_u; u++) {
    if (!occupied.has(u)) {
      available.push(u);
    }
  }
  
  console.log(`机柜 ${rack.value.id} 总U数: ${rack.value.total_u}`);
  console.log(`已占用位置: ${Array.from(occupied).sort((a, b) => a - b).join(', ')}U`);
  console.log(`可用位置: ${available.join(', ')}U`);
  
  return available;
});

// 根据使用率获取进度条颜色
const getProgressColor = (percentage) => {
  if (percentage < 60) return '#67c23a';
  if (percentage < 80) return '#e6a23c';
  return '#f56c6c';
};

// 获取温度状态描述
const getTemperatureStatus = (temp) => {
  if (temp < 18) return '温度过低';
  if (temp > 28) return '温度过高';
  return '温度正常';
};

// 获取设备类型标签类型
const getDeviceTypeTagType = (type) => {
  switch (type) {
    case 'storage': return '';
    case 'server': return 'info';
    case 'network': return 'success';
    case 'security': return 'danger';
    default: return '';
  }
};

// 获取设备类型标签文本
const getDeviceTypeLabel = (type) => {
  switch (type) {
    case 'storage': return '存储';
    case 'server': return '服务器';
    case 'network': return '网络';
    case 'security': return '安全';
    default: return '其他';
  }
};

// 处理设备操作
const handleDeviceAction = (device) => {
  // 查找设备详细信息 - 支持传入设备对象或设备ID
  let deviceDetails = device;
  if (typeof device === 'string') {
    // 如果传入的是设备ID，则查找对应的设备对象
    deviceDetails = devices.value.find(d => d.id === device);
  }
  
  if (!deviceDetails) {
    ElMessage.warning('找不到设备详细信息');
    return;
  }

  console.log('设备详情:', deviceDetails);

  // 显示设备详情对话框
  ElMessageBox.alert(
    `<div class="device-details">
      <div class="detail-item"><span class="label">设备名称:</span> ${deviceDetails.name}</div>
      <div class="detail-item"><span class="label">设备类型:</span> ${getDeviceTypeLabel(deviceDetails.type)}</div>
      <div class="detail-item"><span class="label">设备型号:</span> ${deviceDetails.model || '-'}</div>
      
      <div class="detail-section">
        <h4>网络信息</h4>
        <div class="detail-item"><span class="label">序列号:</span> <span class="highlight">${deviceDetails.serial_number || '-'}</span></div>
        <div class="detail-item"><span class="label">IP地址:</span> <span class="highlight">${deviceDetails.ip_address || '-'}</span></div>
      </div>
      
      <div class="detail-section">
        <h4>物理信息</h4>
        <div class="detail-item"><span class="label">位置:</span> ${deviceDetails.position}U</div>
        <div class="detail-item"><span class="label">高度:</span> ${deviceDetails.u_size}U</div>
        <div class="detail-item"><span class="label">功率:</span> ${deviceDetails.power}W</div>
        <div class="detail-item"><span class="label">状态:</span> <span class="${deviceDetails.status === 'online' ? 'status-online' : 'status-offline'}">${deviceDetails.status === 'online' ? '在线' : '离线'}</span></div>
      </div>
      
      <div class="detail-section">
        <h4>备注</h4>
        <div class="detail-item description">${deviceDetails.description || '-'}</div>
      </div>
    </div>`,
    '设备详情',
    {
      dangerouslyUseHTMLString: true,
      confirmButtonText: '关闭',
      customClass: 'device-details-dialog'
    }
  );
};

// 返回上一页
const goBack = () => {
  router.push('/rack-management');
};

// 获取机柜信息
const fetchRackDetail = async (rackId) => {
  loading.value = true;
  try {
    console.log(`正在获取机柜详情，ID: ${rackId}`);
    console.log(`请求URL: ${API_URL}/${rackId}`);
    
    const response = await axios.get(`${API_URL}/${rackId}`);
    console.log("API返回状态码:", response.status);
    console.log("获取到的机柜数据:", response.data);
    
    if (response.data) {
      rack.value = response.data;
      
      // 如果机柜详情中已经包含设备列表，则直接使用
      if (response.data.devices && Array.isArray(response.data.devices) && response.data.devices.length > 0) {
        console.log(`机柜详情包含 ${response.data.devices.length} 个设备`);
        devices.value = response.data.devices;
      } else {
        console.log("机柜详情中没有设备列表或为空，尝试单独获取设备列表");
        
        // 检查设备数量与实际设备列表是否一致
        if (response.data.device_count > 0) {
          console.log(`机柜报告有 ${response.data.device_count} 个设备，但设备列表为空，尝试强制获取设备列表`);
        }
        
        // 尝试单独获取设备列表，如果失败也不会影响主界面显示
        try {
          await fetchDevices(rackId);
        } catch (deviceError) {
          console.error("获取设备列表失败，使用空列表", deviceError);
          devices.value = [];
          
          // 如果获取失败，尝试创建一个测试设备来验证API是否正常工作
          if (response.data.device_count > 0) {
            console.log("尝试创建测试设备来验证API...");
            try {
              await testDeviceAPI(rackId);
            } catch (testError) {
              console.error("测试设备API失败:", testError);
            }
          }
        }
      }
    }
    loading.value = false;
  } catch (error) {
    console.error("获取机柜详情失败:", error);
    console.error("错误详情:", error.response?.data || error.message);
    console.error("请求配置:", error.config);
    
    if (error.response?.status === 404) {
      ElMessage.error(`机柜ID ${rackId} 不存在，将返回机柜列表页面`);
      setTimeout(() => router.push('/rack-management'), 2000);
    } else {
      ElMessage.error(`获取机柜详情失败: ${error.response?.data?.detail || error.message}`);
    }
    
    loading.value = false;
  }
};

// 测试设备API是否正常工作
const testDeviceAPI = async (rackId) => {
  try {
    // 创建一个测试设备
    const testDevice = {
      name: "API测试设备",
      type: "server",
      position: 1,
      u_size: 1,
      status: "offline",
      description: "API测试用，将被删除"
    };
    
    console.log(`尝试为机柜 ${rackId} 创建测试设备...`);
    console.log("测试设备数据:", testDevice);
    
    // 尝试不同的API端点格式
    try {
      const testResponse = await axios.post(`${API_URL}/${rackId}/devices`, testDevice);
      console.log("测试设备创建成功:", testResponse.data);
      
      // 创建成功后立即删除测试设备
      if (testResponse.data && testResponse.data.id) {
        console.log(`删除测试设备 ${testResponse.data.id}...`);
        await axios.delete(`${API_URL}/${rackId}/devices/${testResponse.data.id}`);
        console.log("测试设备删除成功");
      }
      
      // 如果测试成功，重新获取设备列表
      await fetchDevices(rackId);
      
      return true;
    } catch (primaryError) {
      console.warn("主要API端点测试失败:", primaryError.message);
      
      // 尝试备用API格式
      try {
        const altResponse = await axios.post(`${API_URL}/devices/${rackId}`, testDevice);
        console.log("备用API端点测试成功:", altResponse.data);
        
        // 如果备用API成功，重新获取设备列表
        await fetchDevices(rackId);
        
        return true;
      } catch (altError) {
        console.error("备用API端点测试也失败:", altError.message);
        throw new Error("所有API端点测试失败");
      }
    }
  } catch (error) {
    console.error("设备API测试失败:", error);
    ElMessage.error(`设备API测试失败: ${error.message}`);
    return false;
  }
};

// 获取设备列表
const fetchDevices = async (rackId) => {
  try {
    // 显示加载消息
    ElMessage.info('正在刷新设备列表...');
    
    console.log(`正在获取机柜 ${rackId} 的设备列表...`);
    console.log(`请求URL: ${API_URL}/${rackId}/devices`);
    
    // 添加更详细的日志
    console.log("发送请求前的时间戳:", new Date().toISOString());
    
    // 添加重试机制
    let retryCount = 0;
    const maxRetries = 3;
    
    let response;
    let success = false;
    let lastError = null;
    
    // 尝试重试逻辑
    while (!success && retryCount <= maxRetries) {
      try {
        if (retryCount > 0) {
          console.log(`第 ${retryCount} 次重试获取设备列表...`);
          // 添加延迟，避免立即重试
          await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
          ElMessage.info(`重试获取设备列表 (${retryCount}/${maxRetries})...`);
        }
        
        // 尝试主要API端点
        response = await axios.get(`${API_URL}/${rackId}/devices`, {
          // 添加更长的超时时间
          timeout: 10000 * (retryCount + 1)
        });
        console.log("主要API端点成功");
        success = true;
      } catch (primaryError) {
        console.warn(`主要API端点失败: ${primaryError.message}`);
        lastError = primaryError;
        
        // 尝试备用API格式
        console.log("尝试备用API端点...");
        try {
          // 尝试备用API格式 - 有些API可能使用不同的URL格式
          response = await axios.get(`${API_URL}/devices/${rackId}`, {
            // 添加更长的超时时间
            timeout: 10000 * (retryCount + 1)
          });
          console.log("备用API端点成功");
          success = true;
        } catch (alternativeError) {
          console.warn(`备用API端点也失败: ${alternativeError.message}`);
          // 如果还有重试次数，继续重试
          retryCount++;
          
          if (retryCount > maxRetries) {
            console.error(`已达到最大重试次数 (${maxRetries})`);
            // 重新抛出原始错误，让外层错误处理机制处理
            throw primaryError;
          }
        }
      }
    }
    
    console.log("获取设备列表成功:", response.data);
    console.log("响应状态:", response.status);
    console.log("响应类型:", typeof response.data);
    console.log("是否为数组:", Array.isArray(response.data));
    
    // 验证设备数据的有效性
    const validateDeviceData = (data) => {
      if (!Array.isArray(data)) {
        console.warn("设备数据不是数组");
        return false;
      }
      
      // 检查数组是否为空
      if (data.length === 0) {
        console.log("设备数组为空");
        return true; // 空数组是有效的，只是没有设备
      }
      
      // 检查第一个设备是否有必要的字段
      const firstDevice = data[0];
      const requiredFields = ['id', 'name', 'position', 'u_size'];
      const missingFields = requiredFields.filter(field => !firstDevice.hasOwnProperty(field));
      
      if (missingFields.length > 0) {
        console.warn(`设备数据缺少必要字段: ${missingFields.join(', ')}`);
        return false;
      }
      
      return true;
    };
    
    if (response.data && validateDeviceData(response.data)) {
      // 对设备数据进行处理，确保位置字段为整数且大于0
      const validatedDevices = response.data.map(device => {
        // 确保位置值合法
        if (!device.position || device.position <= 0) {
          console.warn(`设备 ${device.name} 位置值无效 (${device.position})，设置为默认值1`);
          device.position = 1;
        }
        
        // 确保尺寸值合法
        if (!device.u_size || device.u_size <= 0) {
          console.warn(`设备 ${device.name} 尺寸值无效 (${device.u_size})，设置为默认值1`);
          device.u_size = 1;
        }
        
        return device;
      });
      
      devices.value = validatedDevices;
      
      // 更新机柜统计数据
      updateRackStats();
      
      // 显示成功消息，包括设备数量
      if (devices.value.length > 0) {
        ElNotification({
          title: '加载成功',
          message: `成功加载 ${devices.value.length} 个设备`,
          type: 'success',
          duration: 3000,
          position: 'bottom-right'
        });
      } else {
        ElMessage.info('机柜中暂无设备');
      }
    } else {
      console.warn("API返回的设备数据无效:", response.data);
      devices.value = [];
      ElMessage.warning('获取设备数据格式异常');
    }
  } catch (error) {
    console.error("获取设备列表失败:", error);
    
    // 处理404错误 - 可能是新创建的机柜还没有设备
    if (error.response?.status === 404) {
      console.log(`机柜 ${rackId} 的设备列表不存在，创建空设备列表`);
      devices.value = [];
      ElMessage.info('机柜中暂无设备，您可以添加新设备');
    } else {
      ElMessage.error(`获取设备列表失败: ${error.response?.data?.detail || error.message}`);
      devices.value = [];
    }
  }
};

// 选择设备
const selectDevice = (deviceId) => {
  selectedDeviceId.value = deviceId;
  // 可以根据需要添加其他设备选择逻辑
};

// 显示添加设备对话框
const showAddDeviceDialog = () => {
  initialPosition.value = null; // 重置初始位置，除非是从空白位置点击添加
  addDeviceDialogVisible.value = true;
};

// 显示导入设备对话框
const showImportDevicesDialog = () => {
  importDevicesDialogVisible.value = true;
};

// 处理编辑设备
const handleEditDevice = (device) => {
  selectedDevice.value = device;
  editDeviceDialogVisible.value = true;
};

// 处理更新设备信息
const handleUpdateDevice = async (updatedDevice) => {
  try {
    console.log('正在更新设备信息:', updatedDevice);
    
    const response = await axios.put(
      `${API_URL}/${rack.value.id}/devices/${updatedDevice.id}`, 
      updatedDevice
    );
    
    if (response.data) {
      // 更新本地设备列表
      const index = devices.value.findIndex(d => d.id === updatedDevice.id);
      if (index !== -1) {
        devices.value[index] = response.data;
      }
      
      // 重新获取机柜数据以确保数据同步
      await fetchRackDetail(rack.value.id);
      
      ElMessage.success(`设备 ${updatedDevice.name} 信息已更新`);
    }
  } catch (error) {
    console.error("更新设备信息失败:", error);
    
    if (error.response) {
      console.error("API响应状态码:", error.response.status);
      console.error("API响应数据:", error.response.data);
      
      const errorMessage = error.response.data?.detail || '未知错误';
      ElMessage.error(`更新设备信息失败: ${errorMessage}`);
    } else {
      ElMessage.error(`更新设备信息失败: ${error.message}`);
    }
  }
};

// 编辑机柜信息
const editRackInfo = () => {
  editRackDialogVisible.value = true;
};

// 处理添加设备
const handleAddDevice = async (newDevice) => {
  try {
    // 将前端的uSize转换为后端API需要的u_size格式
    const deviceData = {
      name: newDevice.name,
      type: newDevice.type,
      model: newDevice.model,
      position: newDevice.position,
      u_size: newDevice.uSize, // 将uSize映射为u_size
      power: newDevice.power,
      weight: newDevice.weight || 0,
      status: newDevice.status || 'online',
      description: newDevice.notes,
      serial_number: newDevice.serialNumber,
      ip_address: newDevice.ipAddress
    };
    
    console.log('发送添加设备请求，数据:', deviceData);
    console.log('请求URL:', `${API_URL}/${rack.value.id}/devices`);
    
    let response;
    try {
      // 尝试主要API端点
      response = await axios.post(
        `${API_URL}/${rack.value.id}/devices`, 
        deviceData
      );
      console.log("主要API端点成功");
    } catch (primaryError) {
      console.warn(`主要API端点失败: ${primaryError.message}`);
      
      // 尝试备用API格式
      console.log("尝试备用API端点...");
      try {
        // 尝试备用API格式 - 有些API可能使用不同的URL格式
        response = await axios.post(`${API_URL}/devices/${rack.value.id}`, deviceData);
        console.log("备用API端点成功");
      } catch (alternativeError) {
        console.warn(`备用API端点也失败: ${alternativeError.message}`);
        // 重新抛出原始错误，让外层错误处理机制处理
        throw primaryError;
      }
    }
    
    if (response.data) {
      devices.value.push(response.data);
      // 更新机柜数据
      fetchRackDetail(rack.value.id);
      ElMessage.success(`设备 ${newDevice.name} 已添加到机柜`);
    }
  } catch (error) {
    console.error("添加设备失败:", error);
    ElMessage.error(`添加设备失败: ${error.response?.data?.detail || '未知错误'}`);
  }
};

// 确认移除设备
const confirmRemoveDevice = (deviceId) => {
  const device = devices.value.find(d => d.id === deviceId);
  if (!device) return;
  
  ElMessageBox.confirm(
    `确定要从机柜中移除设备 ${device.name} 吗？该设备也将从设备管理系统中删除。`,
    '移除设备',
    {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
      dangerouslyUseHTMLString: true,
      distinguishCancelAndClose: true
    }
  ).then(() => {
    removeDevice(deviceId);
  }).catch(() => {
    // 用户取消操作
  });
};

// 移除设备
const removeDevice = async (deviceId, rackId = rack.value.id) => {
  try {
    console.log(`尝试移除设备，设备ID: ${deviceId}, 机柜ID: ${rackId}`);
    
    // 确保rack.id有值
    if (!rackId) {
      console.error("错误: 机柜ID为空");
      ElMessage.error("无法移除设备: 机柜ID不存在");
      return;
    }
    
    // 获取要删除的设备信息，用于后续在设备管理系统中查找
    const deviceToDelete = devices.value.find(d => d.id === deviceId);
    if (!deviceToDelete) {
      console.error(`未找到ID为 ${deviceId} 的设备`);
      ElMessage.error("未找到要删除的设备");
      return;
    }
    
    // 构建完整URL路径，并确保路径正确
    const apiUrl = `${API_URL}/${rackId}/devices/${deviceId}`;
    console.log(`API请求URL: ${apiUrl}`);
    
    const response = await axios.delete(apiUrl);
    console.log("API请求成功:", response.status);
    
    // 更新本地数据
    devices.value = devices.value.filter(d => d.id !== deviceId);
    
    // 重新获取机柜数据以确保数据同步
    await fetchRackDetail(rackId);
    
    ElMessage.success('设备已从机柜中移除');
    
    // 尝试从设备管理系统中也删除该设备
    try {
      await deleteFromDeviceManagementSystem(deviceToDelete);
    } catch (deviceDeleteError) {
      console.error("从设备管理系统删除设备失败:", deviceDeleteError);
      // 不影响主流程，只显示警告
      ElMessage.warning(`设备已从机柜中移除，但从设备管理系统中删除失败: ${deviceDeleteError.message}`);
    }
  } catch (error) {
    console.error("移除设备失败:", error);
    
    // 更详细的错误信息
    if (error.response) {
      // 服务器返回了错误状态码
      console.error("API响应状态码:", error.response.status);
      console.error("API响应数据:", error.response.data);
      
      const errorMessage = error.response.data?.detail || '未知错误';
      ElMessage.error(`移除设备失败: ${errorMessage}`);
    } else if (error.request) {
      // 请求已发送但没有收到响应
      console.error("未收到API响应");
      ElMessage.error("移除设备失败: 服务器无响应");
    } else {
      // 请求配置出错
      console.error("请求配置错误:", error.message);
      ElMessage.error(`移除设备失败: ${error.message}`);
    }
  }
};

// 从设备管理系统中删除设备
const deleteFromDeviceManagementSystem = async (rackDevice) => {
  try {
    console.log("尝试从设备管理系统中删除设备:", rackDevice);
    
    if (!rackDevice || (!rackDevice.ip_address && !rackDevice.name)) {
      console.log("设备没有IP地址或名称，无法在设备管理系统中查找");
      return;
    }
    
    const axios = (await import('axios')).default;
    
    // 先尝试通过IP地址查找设备
    if (rackDevice.ip_address) {
      try {
        console.log(`通过IP地址 ${rackDevice.ip_address} 查找设备`);
        const devicesResponse = await axios.get(`http://localhost:5888/api/direct-devices`);
        
        if (devicesResponse.data && Array.isArray(devicesResponse.data)) {
          // 查找匹配IP地址的设备
          const matchingDevice = devicesResponse.data.find(d => 
            d.ip_address === rackDevice.ip_address
          );
          
          if (matchingDevice) {
            console.log("找到匹配的设备:", matchingDevice);
            // 删除设备
            await axios.delete(`http://localhost:5888/api/devices/${matchingDevice.id}`);
            console.log(`已从设备管理系统中删除设备 ${matchingDevice.id}`);
            ElMessage.success(`设备 ${matchingDevice.name || rackDevice.name} 已从设备管理系统中删除`);
            return;
          }
        }
      } catch (ipError) {
        console.error("通过IP地址查找设备失败:", ipError);
      }
    }
    
    // 如果通过IP地址没找到，尝试通过名称查找
    if (rackDevice.name) {
      try {
        console.log(`通过名称 ${rackDevice.name} 查找设备`);
        const devicesResponse = await axios.get(`http://localhost:5888/api/direct-devices`);
        
        if (devicesResponse.data && Array.isArray(devicesResponse.data)) {
          // 查找匹配名称的设备
          const matchingDevice = devicesResponse.data.find(d => 
            d.name === rackDevice.name
          );
          
          if (matchingDevice) {
            console.log("找到匹配的设备:", matchingDevice);
            // 删除设备
            await axios.delete(`http://localhost:5888/api/devices/${matchingDevice.id}`);
            console.log(`已从设备管理系统中删除设备 ${matchingDevice.id}`);
            ElMessage.success(`设备 ${matchingDevice.name} 已从设备管理系统中删除`);
            return;
          }
        }
      } catch (nameError) {
        console.error("通过名称查找设备失败:", nameError);
      }
    }
    
    // 如果通过序列号匹配（设备ID）
    if (rackDevice.serial_number) {
      try {
        console.log(`通过序列号 ${rackDevice.serial_number} 查找设备`);
        const devicesResponse = await axios.get(`http://localhost:5888/api/direct-devices`);
        
        if (devicesResponse.data && Array.isArray(devicesResponse.data)) {
          // 查找匹配序列号的设备
          const matchingDevice = devicesResponse.data.find(d => 
            rackDevice.serial_number === d.id.toString()
          );
          
          if (matchingDevice) {
            console.log("找到匹配的设备:", matchingDevice);
            // 删除设备
            await axios.delete(`http://localhost:5888/api/devices/${matchingDevice.id}`);
            console.log(`已从设备管理系统中删除设备 ${matchingDevice.id}`);
            ElMessage.success(`设备 ${matchingDevice.name} 已从设备管理系统中删除`);
            return;
          }
        }
      } catch (serialError) {
        console.error("通过序列号查找设备失败:", serialError);
      }
    }
    
    console.log("未在设备管理系统中找到匹配的设备");
  } catch (error) {
    console.error("从设备管理系统删除设备时出错:", error);
    throw error;
  }
};

// 处理更新机柜信息
const handleUpdateRack = async (updatedRack) => {
  try {
    const response = await axios.put(
      `${API_URL}/${rack.value.id}`, 
      updatedRack
    );
    
    if (response.data) {
      rack.value = response.data;
      ElMessage.success('机柜信息已更新');
    }
  } catch (error) {
    console.error("更新机柜信息失败:", error);
    ElMessage.error("更新机柜信息失败");
  }
};

// 处理点击空白U位添加设备
const handleAddDeviceAtPosition = ({ position, rackId }) => {
  console.log(`在位置 ${position}U 添加设备, 机柜ID: ${rackId}`);
  // 设置初始位置
  initialPosition.value = position;
  // 打开添加设备对话框
  addDeviceDialogVisible.value = true;
};

// 处理导入完成
const handleImportCompleted = async () => {
  ElMessage.success('设备导入完成，正在刷新设备列表...');
  // 重新获取设备列表
  await fetchDevices(rack.value.id);
};

// 初始化
onMounted(async () => {
  const rackId = route.params.id;
  if (rackId) {
    console.log(`初始化机柜详情页面，机柜ID: ${rackId}`);
    
    try {
      // 先获取机柜详情
      await fetchRackDetail(rackId);
      
      // 如果机柜详情中不包含设备或设备为空数组，尝试单独获取设备列表
      if (!devices.value || devices.value.length === 0) {
        try {
          console.log("尝试单独获取设备列表");
          await fetchDevices(rackId);
        } catch (deviceError) {
          console.error("设备数据获取失败，将显示空列表", deviceError);
          // 即使获取设备失败，也不阻止页面加载
          devices.value = [];
        }
      }
      
      // 初始化完成后，更新机柜的used_u和device_count
      updateRackStats();
    } catch (error) {
      console.error("页面初始化失败:", error);
      // 错误处理在fetchRackDetail中已完成
    }
  } else {
    ElMessage.error('未指定机柜ID');
    router.push('/rack-management');
  }
});

// 更新机柜统计数据
const updateRackStats = () => {
  if (devices.value && devices.value.length > 0) {
    // 计算已使用的U位
    let totalUsedU = 0;
    devices.value.forEach(device => {
      if (device.u_size) {
        totalUsedU += device.u_size;
      }
    });
    
    // 更新机柜统计数据
    rack.value.used_u = totalUsedU;
    rack.value.device_count = devices.value.length;
    
    console.log(`更新机柜统计: ${devices.value.length}个设备，使用${totalUsedU}U空间`);
  } else {
    // 如果没有设备，将统计数据设为0
    rack.value.used_u = 0;
    rack.value.device_count = 0;
  }
};
</script>

<style scoped>
.rack-detail-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.back-button {
  margin-right: 20px;
}

.rack-title {
  flex-grow: 1;
}

.rack-title h1 {
  margin: 0;
  font-size: 24px;
  display: flex;
  align-items: center;
}

.rack-location-label {
  font-size: 14px;
  color: #909399;
  font-weight: normal;
  margin-left: 12px;
}

.rack-subtitle {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.rack-status {
  display: flex;
  gap: 10px;
  align-items: center;
}

.stat-cards {
  margin-bottom: 20px;
}

.stat-card {
  height: 120px;
}

.card-content {
  display: flex;
  height: 100%;
  justify-content: space-between;
  align-items: center;
}

.card-value-section {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
}

.card-label {
  font-size: 14px;
  color: #909399;
}

.card-extra-info {
  font-size: 12px;
  color: #909399;
}

.card-progress {
  width: 150px;
  margin-top: 5px;
}

.card-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #f0f5ff;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #409eff;
}

.content-card {
  margin-top: 20px;
}

/* 新的设备视图布局样式 */
.device-view-container {
  display: flex;
  height: 600px;
}

.device-list-panel {
  width: 400px;
  border-right: 1px solid #ebeef5;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 12px 16px;
  border-bottom: 1px solid #ebeef5;
}

.device-count {
  font-size: 14px;
  color: #606266;
}

.empty-hint {
  font-size: 12px;
  color: #909399;
  margin-left: 8px;
}

.device-table {
  flex: 1;
  overflow-y: auto;
}

.device-name {
  display: flex;
  align-items: center;
}

.device-status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #c0c4cc;
  margin-right: 8px;
}

.device-status-dot.online {
  background-color: #67c23a;
}

.add-device-btn-container {
  padding: 16px;
  border-top: 1px solid #ebeef5;
  display: flex;
  gap: 10px;
}

.add-device-btn {
  flex: 2;
}

.import-btn {
  flex: 1;
}

.refresh-btn {
  flex: 1;
}

.rack-visualization-panel {
  flex: 1;
  padding: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f9f9f9;
}

.rack-visualization-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}



/* 设备详情对话框样式 */
:deep(.device-details-dialog) {
  .el-message-box__content {
    padding: 20px;
  }
}

.device-details {
  margin-top: 10px;
  
  .detail-item {
    margin-bottom: 8px;
    line-height: 1.5;
    display: flex;
  }
  
  .label {
    font-weight: bold;
    color: #606266;
    min-width: 80px;
    display: inline-block;
    margin-right: 8px;
  }
  
  .detail-section {
    margin-top: 15px;
    border-top: 1px solid #ebeef5;
    padding-top: 10px;
    
    h4 {
      margin-top: 0;
      margin-bottom: 10px;
      font-size: 14px;
      color: #909399;
    }
  }
  
  .highlight {
    color: #409eff;
    font-weight: bold;
  }
  
  .status-online {
    color: #67c23a;
    font-weight: bold;
  }
  
  .status-offline {
    color: #f56c6c;
    font-weight: bold;
  }
  
  .description {
    white-space: pre-line;
  }
}
</style>